'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { DownloadIcon, SaveIcon, ImageIcon, ArrowRightIcon } from 'lucide-react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';




import { toast } from 'sonner';

interface ResultDisplayProps {
  originalImage: {
    file: File;
    url: string;
    width: number;
    height: number;
  };
  resultData: {
    imageUrl: string;
    model: string;
    prompt: string;
    tokenUsage?: {
      total: number;
      input: number;
      output: number;
    } | null;
  };
  onReset: () => void;
  onSave: (folder?: string) => Promise<void>;
}

export default function ResultDisplay({
  originalImage,
  resultData,
  onSave
}: ResultDisplayProps) {
  const [activeTab, setActiveTab] = useState('result');
  const [isSaving, setIsSaving] = useState(false);






  const handleDownload = () => {
    // Create an anchor element and trigger download
    const a = document.createElement('a');
    a.href = resultData.imageUrl;
    a.download = `edited_${originalImage.file.name}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Save to root folder by default
      await onSave("");
      toast.success("Image saved", {
        description: "Image has been saved to root folder"
      });
    } catch (error) {
      console.error('Error saving image:', error);
      toast.error("Error saving image", {
        description: error instanceof Error ? error.message : "An unknown error occurred"
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-6">
          <TabsTrigger value="result" className="flex items-center gap-2">
            <ImageIcon className="h-4 w-4" />
            Result
          </TabsTrigger>
          <TabsTrigger value="comparison" className="flex items-center gap-2">
            <ImageIcon className="h-4 w-4" />
            Comparison
          </TabsTrigger>
        </TabsList>

        <TabsContent value="result" className="mt-0">
          <Card className="p-6 flex flex-col items-center justify-center border-0 shadow-none">
            <div className="w-full max-w-2xl">
              <img 
                src={resultData.imageUrl} 
                alt="Generated result" 
                className="w-full h-auto object-contain rounded-lg shadow-md"
              />
            </div>

            <div className="w-full max-w-2xl mt-4 space-y-2">
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline">{resultData.model}</Badge>
                {resultData.tokenUsage && (
                  <Badge variant="outline">{resultData.tokenUsage.total} tokens</Badge>
                )}
              </div>

              <div className="text-sm text-muted-foreground mt-1">
                <p><strong>Prompt:</strong> {resultData.prompt}</p>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="comparison" className="mt-0">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 relative">
            <Card className="p-4 flex flex-col items-center border-0 shadow-none">
              <div className="relative w-full flex items-center justify-center bg-muted/30 rounded-lg overflow-hidden p-2">
                <img 
                  src={originalImage.url} 
                  alt="Original" 
                  className="max-w-full max-h-full object-contain rounded-lg"
                />
              </div>
            </Card>

            {/* Arrow pointing to generated image */}
            <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10 hidden sm:flex items-center justify-center">
              <div className="bg-primary/10 backdrop-blur-sm rounded-full p-2">
                <ArrowRightIcon className="h-6 w-6 text-primary" />
              </div>
            </div>

            <Card className="p-4 flex flex-col items-center border-0 shadow-none">
              <div className="relative w-full flex items-center justify-center bg-muted/30 rounded-lg overflow-hidden p-2">
                <img 
                  src={resultData.imageUrl} 
                  alt="Generated" 
                  className="max-w-full max-h-full object-contain rounded-lg"
                />
              </div>
            </Card>
          </div>

          <div className="mt-4 p-4 bg-muted/20 rounded-md">
            <p className="text-sm text-muted-foreground"><strong>Prompt:</strong> {resultData.prompt}</p>

            {resultData.tokenUsage && (
              <div className="mt-2 text-xs text-muted-foreground grid grid-cols-3 gap-2">
                <div>
                  <p>Total Tokens</p>
                  <p className="font-mono">{resultData.tokenUsage.total}</p>
                </div>
                <div>
                  <p>Input Tokens</p>
                  <p className="font-mono">{resultData.tokenUsage.input}</p>
                </div>
                <div>
                  <p>Output Tokens</p>
                  <p className="font-mono">{resultData.tokenUsage.output}</p>
                </div>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      <div className="flex flex-wrap gap-2 mt-4">
        <Button 
          variant="outline" 
          onClick={handleDownload}
          className="gap-2"
        >
          <DownloadIcon className="h-4 w-4" />
          Download
        </Button>
        


        <Button 
          onClick={handleSave}
          className="gap-2"
          disabled={isSaving}
        >
          <SaveIcon className="h-4 w-4" />
          {isSaving ? 'Saving...' : 'Save to Gallery'}
        </Button>
      </div>
    </div>
  );
}